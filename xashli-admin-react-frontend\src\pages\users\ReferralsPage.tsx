import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Search,
  Filter,
  TrendingUp,
  Users,
  Wallet,
  Eye,
  Calendar,
} from 'lucide-react';
import { userService } from '@/services/user';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading';
import { MainLayout } from '@/components/layout/MainLayout';
import { formatCurrencyAmount, formatDate } from '@/utils/format';
import type {
  RefereesApiResponse,
  ReferralInfoApiResponse,
  BonusesApiResponse,
  ExtendedRefereeData,
  ReferralFilters,
} from '@/types/referral';

export default function ReferralsPage() {
  const { id } = useParams<{ id: string }>();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ReferralFilters>({
    date_from: undefined,
    date_to: undefined,
  });

  // Fetch referees data for a specific user (admins can specify user_id)
  const {
    data: refereesData,
    isLoading,
  } = useQuery({
    queryKey: ['referees', { userId: id }],
    queryFn: () => userService.getReferees(id ? { userId: id } : undefined),
  });

  // Fetch referral bonuses for commission data
  const { data: bonusesData } = useQuery({
    queryKey: ['referral-bonuses', { userId: id }],
    queryFn: () =>
      userService.getReferralBonuses(id ? { userId: id } : undefined),
  });

  // Fetch referral info (basic stats) - now supports querying specific users
  const { data: stats } = useQuery({
    queryKey: ['referral-info', { userId: id }],
    queryFn: () => userService.getReferralInfo(id ? { userId: id } : undefined),
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled locally by filtering the allReferees array
    // No need for URL search params in this context
  };

  const handleFilterChange = (key: string, value: string | undefined) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  if (isLoading && !refereesData) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <LoadingSpinner size="lg" className="mx-auto mb-4" />
              <p className="text-gray-600">Loading referrals data...</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Process the API response
  const refereesResponse = refereesData?.data as
    | RefereesApiResponse
    | undefined;
  const bonusesResponse = bonusesData?.data as BonusesApiResponse | undefined;
  const bonuses = bonusesResponse || { bonuses: [], summary: {} };
  const statsData = stats?.data as ReferralInfoApiResponse | undefined;

  // Use the merged and sorted referees list from the API
  const allReferees: ExtendedRefereeData[] = (refereesResponse?.referees?.all || []).map(referee => ({
    ...referee,
    level: referee.level || 1, // Ensure level is always defined
  }));

  // Filter referees based on search term and date filters
  const filteredReferees = allReferees.filter(referee => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        referee.full_name.toLowerCase().includes(searchLower) ||
        referee.email.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Date filters
    if (filters.date_from) {
      const refereeDate = new Date(referee.created_at);
      const fromDate = new Date(filters.date_from);
      if (refereeDate < fromDate) return false;
    }

    if (filters.date_to) {
      const refereeDate = new Date(referee.created_at);
      const toDate = new Date(filters.date_to);
      if (refereeDate > toDate) return false;
    }

    return true;
  });
  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto space-y-6 mb-6">
        {' '}
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Referral Management
              {statsData && (
                <span className="text-lg font-normal text-gray-600 ml-2">
                  - {statsData.target_user.full_name}
                </span>
              )}
            </h1>
            <p className="text-sm text-gray-600">
              {statsData
                ? `Viewing referrals for ${statsData.target_user.full_name} (${statsData.target_user.email})`
                : 'Track and manage user referrals and commissions'}
            </p>
          </div>
        </div>

      </div>{' '}
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Referrals
            </CardTitle>
            <div className="p-2 rounded-full bg-blue-50">
              <Users className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {refereesResponse?.counts?.total || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Referrals
            </CardTitle>
            <div className="p-2 rounded-full bg-green-50">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {allReferees.filter(ref => ref.is_active).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Commission
            </CardTitle>
            <div className="p-2 rounded-full bg-yellow-50">
              <Wallet className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(bonuses?.summary?.fiat?.count || 0) +
                (bonuses?.summary?.crypto?.count || 0)}
            </div>
            <div className="mt-1 space-y-1">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Fiat:</span>
                <span>
                  {formatCurrencyAmount(
                    bonuses?.summary?.fiat?.total_referral_bonus_amount || 0,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Crypto:</span>
                <span>
                  {formatCurrencyAmount(
                    bonuses?.summary?.crypto?.total_referral_bonus_amount || 0,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Level 1 Referrals
            </CardTitle>
            <div className="p-2 rounded-full bg-purple-50">
              <Calendar className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {refereesResponse?.counts?.level1 || 0}
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="space-y-6">
          {/* Filters and Search */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Search
              </CardTitle>
            </CardHeader>{' '}
            <CardContent>
              {' '}
              <form onSubmit={handleSearch} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search referrals..."
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <Input
                    type="date"
                    placeholder="Date from"
                    value={filters.date_from || ''}
                    onChange={e =>
                      handleFilterChange(
                        'date_from',
                        e.target.value || undefined
                      )
                    }
                  />

                  <Input
                    type="date"
                    placeholder="Date to"
                    value={filters.date_to || ''}
                    onChange={e =>
                      handleFilterChange('date_to', e.target.value || undefined)
                    }
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Button type="submit" size="sm">
                    Apply Filters
                  </Button>{' '}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setFilters({
                        date_from: undefined,
                        date_to: undefined,
                      });
                    }}
                  >
                    Clear All
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>{' '}
          {/* Referrals Table */}
          <Card>
            <CardHeader>
              <CardTitle>Referral Records</CardTitle>
              <CardDescription>
                {filteredReferees.length !== allReferees.length
                  ? `Showing ${filteredReferees.length} of ${allReferees.length} referrals (filtered)`
                  : `Showing ${allReferees.length} referrals`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner size="md" />
                </div>
              ) : filteredReferees.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No referrals found
                  </h3>
                  <p className="text-gray-500">
                    {allReferees.length === 0
                      ? 'No referral data matches your current filters.'
                      : 'No referrals match your search criteria. Try adjusting your filters.'}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full min-w-[800px] table-fixed">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-500 w-24">
                          Level
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 min-w-[180px]">
                          Referred User
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 min-w-[200px]">
                          Email
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 w-32">
                          Date Joined
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 w-24 text-center">
                          Referees
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 w-24">
                          Status
                        </th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 w-20">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredReferees.map((referee: ExtendedRefereeData) => (
                        <tr
                          key={referee.id}
                          className="border-b border-gray-100 hover:bg-gray-50"
                        >
                          <td className="py-3 px-4 w-24">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium whitespace-nowrap ${
                                referee.level === 1
                                  ? 'bg-blue-100 text-blue-800'
                                  : referee.level === 2
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-purple-100 text-purple-800'
                              }`}
                            >
                              Level {referee.level}
                            </span>
                          </td>
                          <td className="py-3 px-4 w-48">
                            <Link
                              to={`/users/${referee.id}`}
                              className="font-medium text-gray-900 hover:text-brand-gold-600 block truncate"
                              title={referee.full_name}
                            >
                              {referee.full_name}
                            </Link>
                          </td>
                          <td className="py-3 px-4 w-64">
                            <span className="text-sm text-gray-500 block truncate" title={referee.email}>
                              {referee.email}
                            </span>
                          </td>
                          <td className="py-3 px-4 w-32">
                            <span className="text-sm text-gray-900 whitespace-nowrap">
                              {formatDate(referee.created_at)}
                            </span>
                          </td>
                          <td className="py-3 px-4 w-24 text-center">
                            <span className="text-sm text-gray-900">
                              {referee.referee_count}
                            </span>
                          </td>
                          <td className="py-3 px-4 w-24">
                            {referee.is_active ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 whitespace-nowrap">
                                Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 whitespace-nowrap">
                                Inactive
                              </span>
                            )}
                          </td>
                          <td className="py-3 px-4 w-20 text-center">
                            <Link to={`/users/${referee.id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
    </MainLayout>
  );
}
