import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { fundService } from '../../services';
import type { CreateFundData } from '../../types';

export function useFundForm() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const createFund = useCallback(
    async (data: CreateFundData) => {
      try {
        setLoading(true);
        const response = await fundService.createFund(data);

        if (response.status === 'success' && response.data) {
          toast.success('Fund created successfully');
          navigate('/funds');
          return true;
        } else {
          toast.error(response.message || 'Failed to create fund');
          return false;
        }
      } catch (error) {
        console.error('Error creating fund:', error);
        toast.error('Failed to create fund');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [navigate]
  );

  return {
    loading,
    createFund,
  };
}
