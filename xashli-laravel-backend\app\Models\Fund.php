<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Fund extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'amount',
        'currency',
        'payment_method_id',
        'platform_fee_percentage',
        'start_date',
        'maturity_date',
        'maturity_days',
        'growth_percentage',
        'growth_amount',
        'referral_bonus_limit',
        'is_eligible_for_withdrawal',
        'amount_matched',
        'status',
        'prev_fund_id',
        'next_fund_id',
        'withdraw_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:8',
        'platform_fee_percentage' => 'decimal:2',
        'growth_percentage' => 'decimal:2',
        'growth_amount' => 'decimal:8',
        'referral_bonus_limit' => 'decimal:8',
        'is_eligible_for_withdrawal' => 'boolean',
        'start_date' => 'datetime',
        'maturity_date' => 'datetime',
        'maturity_days' => 'integer',
        'amount_matched' => 'decimal:8',
        'status' => 'string',
        'currency' => 'string',
    ];

    /**
     * Get the user that owns the fund.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment method for the fund.
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Get the withdraws for the fund.
     */
    public function withdraws(): HasMany
    {
        return $this->hasMany(Withdraw::class);
    }

    /**
     * Get the user withdraw for the fund.
     * Note: This is a soft reference without foreign key constraint.
     */
    public function userWithdraw(): BelongsTo
    {
        return $this->belongsTo(Withdraw::class, 'withdraw_id');
    }

    /**
     * Get the payment matches for the fund.
     */
    public function paymentMatches(): HasMany
    {
        return $this->hasMany(PaymentMatch::class);
    }

    /**
     * Get the referral bonuses for the fund.
     */
    public function referralBonuses(): HasMany
    {
        return $this->hasMany(ReferralBonus::class);
    }

    /**
     * Get the previous fund.
     */
    public function previousFund(): BelongsTo
    {
        return $this->belongsTo(Fund::class, 'prev_fund_id');
    }

    /**
     * Get the next fund.
     */
    public function nextFund(): BelongsTo
    {
        return $this->belongsTo(Fund::class, 'next_fund_id');
    }

    /**
     * Check if the fund has a next fund linked.
     */
    public function hasNextFund(): bool
    {
        return $this->next_fund_id !== null;
    }

    /**
     * Check if the fund is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the fund is matched.
     */
    public function isMatched(): bool
    {
        return $this->status === 'matched';
    }

    /**
     * Check if the fund is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the fund is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the fund is for fiat.
     */
    public function isFiat(): bool
    {
        return $this->currency === 'fiat';
    }

    /**
     * Check if the fund is for crypto.
     */
    public function isCrypto(): bool
    {
        return $this->currency === 'crypto';
    }

    /**
     * Check if the fund is eligible for withdrawal.
     */
    public function isEligibleForWithdrawal(): bool
    {
        return $this->is_eligible_for_withdrawal;
    }

    /**
     * Check if the fund is matured.
     */
    public function isMatured(): bool
    {
        return $this->maturity_date !== null && now()->gte($this->maturity_date);
    }

    /**
     * Check if the fund can be withdrawn.
     * A fund can be withdrawn if it's matured, has a next fund that's completed, and hasn't been withdrawn yet.
     */
    public function canBeWithdrawn(): bool
    {
        return $this->isMatured() &&
               $this->hasNextFund() &&
               $this->nextFund?->isCompleted() &&
               $this->withdraw_id === null;
    }

    /**
     * Check if the fund has a user withdrawal.
     */
    public function hasUserWithdraw(): bool
    {
        return $this->withdraw_id !== null;
    }

    /**
     * Check if all payment matches are settled (confirmed).
     * Cancelled matches are excluded as they don't need to be settled.
     */
    public function arePaymentMatchesSettled(): bool
    {
        return $this->paymentMatches()
            ->where('status', '!=', 'cancelled')
            ->where('status', '!=', 'confirmed')
            ->count() === 0;
    }

    /**
     * Calculate the total return percentage (growth + max referral bonus).
     */
    public function getTotalReturnPercentage(): float
    {
        $growthPercentage = $this->growth_amount / $this->amount;
        $referralBonusPercentage = $this->referral_bonus_limit / $this->amount;

        return $growthPercentage + $referralBonusPercentage;
    }

    /**
     * Check if the total return exceeds 100% of the fund amount.
     */
    public function totalReturnExceedsLimit(): bool
    {
        return $this->getTotalReturnPercentage() > 1.0;
    }

    /**
     * Check if the fund has at least one confirmed payment match.
     */
    public function hasConfirmedPayments(): bool
    {
        return $this->paymentMatches()->where('is_payment_sent_confirmed', true)->exists();
    }

    /**
     * Get the platform fees for the fund.
     */
    public function platformFees(): HasMany
    {
        return $this->hasMany(PlatformFee::class);
    }

    /**
     * Check if platform fee has been collected for this fund.
     */
    public function hasPlatformFeeCollected(): bool
    {
        return $this->platformFees()->exists();
    }

    /**
     * Calculate platform fee amount for this fund.
     */
    public function calculatePlatformFeeAmount(): float
    {
        return $this->amount * ($this->platform_fee_percentage / 100);
    }

    /**
     * Check if all payment matches for the fund are complete and update fund status accordingly.
     * A fund is considered complete when all matches have:
     * 1. Payment sent confirmation
     * 2. Payment received confirmation
     * 3. Payment proof uploaded
     *
     * When a fund becomes completed, its start_date and maturity_date are set based on completion time.
     */
    public function checkAndUpdateCompletion(): bool
    {
        // Get all payment matches for this fund, excluding cancelled ones
        $paymentMatches = $this->paymentMatches()->where('status', '!=', 'cancelled')->get();

        // Check if all non-cancelled matches are complete
        $allMatchesComplete = $paymentMatches->every(function ($match) {
            return $match->isComplete();
        });

        // Only mark fund as completed if all matches are complete and fund is currently matched
        if ($allMatchesComplete && $this->status === 'matched' && $paymentMatches->count() > 0) {
            $this->status = 'completed';

            // Set start_date and maturity_date when fund becomes completed
            $completionTime = now();
            if ($this->start_date === null) {
                $this->start_date = $completionTime;
            }
            if ($this->maturity_date === null) {
                $this->maturity_date = $completionTime->addDays($this->maturity_days);
            }

            $this->save();

            return true;
        }

        return false;
    }

    /**
     * Process referral bonuses for the fund.
     * Only processes bonuses for completed funds with confirmed payments.
     */
    public function processReferralBonuses(): void
    {
        // Only process referral bonuses for completed funds with confirmed payments
        if ($this->status !== 'completed' || ! $this->hasConfirmedPayments()) {
            return;
        }

        // Check if referral bonuses have already been processed for this fund
        if ($this->hasReferralBonuses()) {
            return;
        }

        $user = User::find($this->user_id);

        if (! $user || ! $user->referrer_id) {
            return;
        }

        // Get referral percentages from settings
        $level1Percentage = PlatformSetting::getSetting('referral_level1_percentage');
        $level2Percentage = PlatformSetting::getSetting('referral_level2_percentage');
        $level3Percentage = PlatformSetting::getSetting('referral_level3_percentage');

        // Initialize user variables
        $level1User = null;
        $level2User = null;

        // Process Level 1 (direct referrer)
        if ($level1Percentage > 0) {
            $level1UserId = $user->referrer_id;
            $level1User = User::find($level1UserId);
            if (! $level1User) {
                return;
            }
            $this->createReferralBonus($level1User->id, $level1Percentage, 1);
        }

        // Process Level 2
        if ($level2Percentage > 0 && $level1User && $level1User->referrer_id) {
            $level2UserId = $level1User->referrer_id;
            $level2User = User::find($level2UserId);
            if (! $level2User) {
                return;
            }
            $this->createReferralBonus($level2User->id, $level2Percentage, 2);
        }

        // Process Level 3
        if ($level3Percentage > 0 && $level2User && $level2User->referrer_id) {
            $level3UserId = $level2User->referrer_id;
            $level3User = User::find($level3UserId);
            if (! $level3User) {
                return;
            }
            $this->createReferralBonus($level3User->id, $level3Percentage, 3);
        }
    }

    /**
     * Create a referral bonus for a specific user.
     */
    private function createReferralBonus(string $userId, float $percentage, int $level): void
    {
        $amount = $this->amount * ($percentage / 100);

        // Use firstOrCreate to avoid duplicates (works with unique constraint)
        $referralBonus = ReferralBonus::firstOrCreate(
            [
                'fund_id' => $this->id,
                'referrer_user_id' => $userId,
                'level' => $level,
            ],
            [
                'referee_user_id' => $this->user_id,
                'percentage' => $percentage,
                'amount' => $amount,
            ]
        );

        // Only update user stats if this is a newly created bonus
        if ($referralBonus->wasRecentlyCreated) {
            // Update user stats
            $userStat = UserStat::firstOrCreate(['user_id' => $userId], [
                'updated_at' => now(),
            ]);
            if ($this->currency === 'fiat') {
                $userStat->incrementAvailableFiatReferralBonus($amount);
            } else {
                $userStat->incrementAvailableCryptoReferralBonus($amount);
            }
        }
    }

    /**
     * Check if referral bonuses have been processed for this fund.
     */
    public function hasReferralBonuses(): bool
    {
        return $this->referralBonuses()->exists();
    }

    /**
     * Check if the fund is considered "running".
     * A running fund is one that is not cancelled and has not matured yet.
     */
    public function isRunning(): bool
    {
        return !$this->isCancelled() && !$this->isMatured();
    }

    /**
     * Get count of running funds for a user.
     * Excludes recommitment funds from the count when a previous fund ID is provided.
     */
    public static function getRunningFundsCount(string $userId, ?string $previousFundId = null): int
    {
        $query = self::where('user_id', $userId)
            ->where('status', '!=', 'cancelled')
            ->where(function ($q) {
                $q->whereNull('maturity_date')
                  ->orWhere('maturity_date', '>', now());
            });

        // Exclude recommitment funds from count when creating a recommitment
        if ($previousFundId) {
            $query->where('prev_fund_id', '!=', $previousFundId);
        }

        return $query->count();
    }
}
