{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/activity-logs/activitylogstable.tsx", "../../src/components/activity-logs/index.ts", "../../src/components/admin-payments/adminpaymentmodal.tsx", "../../src/components/admin-payments/adminpaymentproofmodal.tsx", "../../src/components/common/protectedroute.tsx", "../../src/components/common/index.ts", "../../src/components/funds/fundfilters.tsx", "../../src/components/funds/fundlist.tsx", "../../src/components/funds/fundstatscards.tsx", "../../src/components/layout/mainlayout.tsx", "../../src/components/layout/sidebar.tsx", "../../src/components/layout/topbar.tsx", "../../src/components/layout/index.ts", "../../src/components/ui/nextmatchingcountdown.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/form.tsx", "../../src/components/ui/index.ts", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/loading.tsx", "../../src/components/ui/pagination.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/withdraws/withdrawfilters.tsx", "../../src/components/withdraws/withdrawlist.tsx", "../../src/components/withdraws/withdrawstatscards.tsx", "../../src/components/withdraws/index.ts", "../../src/config/platformsettings.ts", "../../src/contexts/authcontext.tsx", "../../src/contexts/sidebarcontext.tsx", "../../src/contexts/index.ts", "../../src/hooks/index.ts", "../../src/hooks/useadminactivitylogs.ts", "../../src/hooks/usedashboard.ts", "../../src/hooks/usedisputes.ts", "../../src/hooks/useplatformfees.ts", "../../src/hooks/useplatformsettings.ts", "../../src/pages/dashboardpage.tsx", "../../src/pages/index.ts", "../../src/pages/activity-logs/activitylogdetailspage.tsx", "../../src/pages/activity-logs/activitylogspage.tsx", "../../src/pages/activity-logs/index.ts", "../../src/pages/auth/forgotpasswordpage.tsx", "../../src/pages/auth/loginpage.tsx", "../../src/pages/auth/resetpasswordpage.tsx", "../../src/pages/auth/index.ts", "../../src/pages/disputes/disputedetailspage.tsx", "../../src/pages/disputes/disputeresolvemodal.tsx", "../../src/pages/disputes/disputespage.tsx", "../../src/pages/disputes/index.ts", "../../src/pages/funds/funddetailspage.tsx", "../../src/pages/funds/fundspage.tsx", "../../src/pages/payment-matches/manualmatchingpage.tsx", "../../src/pages/payment-matches/paymentmatchdetailspage.tsx", "../../src/pages/payment-matches/paymentmatchstatisticspage.tsx", "../../src/pages/payment-matches/paymentmatchespage.tsx", "../../src/pages/payment-matches/index.ts", "../../src/pages/platform-fees/platformfeespage.tsx", "../../src/pages/platform-fees/index.ts", "../../src/pages/settings/fundingschedulepage.tsx", "../../src/pages/settings/platformpaymentmethodspage.tsx", "../../src/pages/settings/settingscategorypage.tsx", "../../src/pages/settings/index.ts", "../../src/pages/users/referralspage.tsx", "../../src/pages/users/userdetailspage.tsx", "../../src/pages/users/usereditpage.tsx", "../../src/pages/users/userslistpage.tsx", "../../src/pages/users/index.ts", "../../src/pages/withdraws/withdrawdetailspage.tsx", "../../src/pages/withdraws/withdrawspage.tsx", "../../src/pages/withdraws/index.ts", "../../src/services/adminactivitylog.ts", "../../src/services/api.ts", "../../src/services/auth.ts", "../../src/services/dashboard.ts", "../../src/services/dispute.ts", "../../src/services/fund.ts", "../../src/services/index.ts", "../../src/services/paymentmatch.ts", "../../src/services/paymentmethod.ts", "../../src/services/platformfeeservice.ts", "../../src/services/platformsettings.ts", "../../src/services/user.ts", "../../src/services/withdraw.ts", "../../src/types/adminactivitylog.ts", "../../src/types/auth.ts", "../../src/types/common.ts", "../../src/types/dashboard.ts", "../../src/types/dispute.ts", "../../src/types/fund.ts", "../../src/types/index.ts", "../../src/types/paymentmatch.ts", "../../src/types/paymentmethod.ts", "../../src/types/platformfee.ts", "../../src/types/platformsettings.ts", "../../src/types/referral.ts", "../../src/types/user.ts", "../../src/types/withdraw.ts", "../../src/utils/cn.ts", "../../src/utils/convert.ts", "../../src/utils/cookies.ts", "../../src/utils/copy.ts", "../../src/utils/format.ts", "../../src/utils/images.ts", "../../src/utils/index.ts", "../../src/utils/toast.ts", "../../src/utils/validation.ts"], "version": "5.8.3"}