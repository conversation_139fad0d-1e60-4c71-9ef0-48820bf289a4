<?php

namespace App\Http\Controllers;

use App\Models\Fund;
use App\Models\PaymentMethod;
use App\Models\PlatformSetting;
use App\Models\ReferralBonus;
use App\Models\User;
use App\Models\UserStat;
use App\Services\PaymentMatchTimeoutService;
use App\Services\UserPrivilegeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class FundController extends Controller
{
    /**
     * Display a listing of funds.
     * Users see only their own funds, admins see all funds.
     */
    public function index(Request $request): JsonResponse
    {
        // Check if user is admin to determine query scope
        if (auth()->user()->isAdmin()) {
            // Admin can see all funds with user relationship and payment matches
            $query = Fund::with([
                'user',
                'paymentMethod',
                'userWithdraw',
                'paymentMatches' => function ($query) {
                    $query->select('id', 'fund_id', 'withdraw_id', 'status', 'created_at')
                        ->with([
                            'fundUser:id,full_name,email',
                            'withdrawUser:id,full_name,email',
                        ]);
                },
            ]);

            // Filter by user_id if provided (admin-specific filter)
            if ($request->has('user_id') && ! empty($request->user_id)) {
                $query->where('user_id', $request->user_id);
            }

            // Filter by user email if provided (admin-specific filter)
            if ($request->has('email') && ! empty($request->email)) {
                $query->whereHas('user', function ($q) use ($request) {
                    $q->where('email', 'like', '%' . $request->email . '%');
                });
            }
        } else {
            // Users only see their own funds with payment matches
            $query = auth()->user()->funds()->with([
                'paymentMethod',
                'userWithdraw',
                'paymentMatches' => function ($query) {
                    $query->select('id', 'fund_id', 'withdraw_id', 'status', 'created_at')
                        ->with([
                            'fundUser:id,full_name,email',
                            'withdrawUser:id,full_name,email',
                        ]);
                },
            ]);
        }

        // Filter by status if provided
        if ($request->has('status') && in_array($request->status, ['pending', 'matched', 'completed', 'cancelled'])) {
            $query->where('status', $request->status);
        }

        // Filter by currency if provided
        if ($request->has('currency') && in_array($request->currency, ['fiat', 'crypto'])) {
            $query->where('currency', $request->currency);
        }

        // Handle sorting
        if ($request->has('sort_field') && $request->has('sort_direction')) {
            $sortField = $request->sort_field;
            $sortDirection = in_array($request->sort_direction, ['asc', 'desc']) ? $request->sort_direction : 'desc';

            // Only allow specific fields for sorting for security
            $allowedSortFields = ['amount', 'created_at', 'status', 'currency'];
            if (in_array($sortField, $allowedSortFields)) {
                $query->orderBy($sortField, $sortDirection);
            } else {
                // Default sorting if invalid field provided
                $query->orderBy('created_at', 'desc');
            }
        } else {
            // Default sorting by created_at in descending order (newest first)
            $query->orderBy('created_at', 'desc');
        }

        // Pagination
        $isAdmin = auth()->user()->isAdmin();
        $perPage = min($request->get('per_page', 15), $isAdmin ? 100 : 50);
        $paginatedFunds = $query->paginate($perPage);

        // Structure response with separate funds and pagination
        $response = [
            'funds' => $paginatedFunds->items(),
            'pagination' => [
                'current_page' => $paginatedFunds->currentPage(),
                'last_page' => $paginatedFunds->lastPage(),
                'per_page' => $paginatedFunds->perPage(),
                'total' => $paginatedFunds->total(),
                'from' => $paginatedFunds->firstItem(),
                'to' => $paginatedFunds->lastItem(),
            ],
        ];

        return $this->success($response, 'Funds retrieved successfully');
    }

    /**
     * Store a newly created fund in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Check user account status first, before any other validation
        $user = auth()->user();

        // Auto-reactivate if deactivation period has expired
        $user->autoReactivateIfExpired();

        if (! $user->is_active) {
            return $this->forbidden('Your account has been deactivated. Please contact support or wait for automatic reactivation.');
        }

        // Check if funding is allowed based on schedule settings
        if (! $this->isFundingAllowedBySchedule()) {
            return $this->forbidden('Funding is currently not allowed. Please check the allowed funding hours and try again.');
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'currency' => 'required|in:fiat,crypto',
            'payment_method_id' => 'required|uuid|exists:payment_methods,id',
            'previous_fund_id' => 'nullable|uuid|exists:funds,id',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Validate amount step (multiples)
        $fiatStep = 10; // NGN must be in multiples of 10
        $cryptoStep = 0.025; // SOL must be in multiples of 0.025

        $step = $request->currency === 'fiat' ? $fiatStep : $cryptoStep;

        // Handle floating point precision for crypto amounts
        if ($request->currency === 'crypto') {
            // Convert to the smallest unit to avoid floating point precision issues
            $amountInSmallestUnit = (int) ($request->amount * 1000); // Convert to millicents
            $stepInSmallestUnit = (int) ($cryptoStep * 1000);
            $isValidMultiple = $amountInSmallestUnit % $stepInSmallestUnit === 0;
        } else {
            $isValidMultiple = fmod($request->amount, $step) === 0.0;
        }

        if (! $isValidMultiple) {
            return $this->error(
                $request->currency === 'fiat'
                    ? 'Amount must be a multiple of NGN ' . number_format($fiatStep, 0)
                    : 'Amount must be a multiple of ' . number_format($cryptoStep, 3) . ' SOL',
                400
            );
        }

        // Get privilege service and calculate/update user privileges
        $privilegeService = new UserPrivilegeService;

        // Calculate and update user privileges before creating fund
        $privilegeService->calculateAndUpdatePrivileges($user);

        // Get amount limits based on currency and user privileges
        $minAmountSetting = $request->currency === 'fiat'
            ? 'minimum_fiat_fund_amount'
            : 'minimum_crypto_fund_amount';

        $minAmount = PlatformSetting::getSetting($minAmountSetting);
        $maxAmount = $privilegeService->getMaximumFundAmount($user, $request->currency);

        // Validate amount against limits
        if ($request->amount < $minAmount) {
            return $this->error('Amount is below the minimum allowed of ' .
                ($request->currency === 'fiat'
                    ? 'NGN ' . number_format($minAmount, 0)
                    : number_format($minAmount, 2) . ' SOL'), 400);
        }

        if ($request->amount > $maxAmount) {
            return $this->error('Amount exceeds the maximum allowed of ' .
                ($request->currency === 'fiat'
                    ? 'NGN ' . number_format($maxAmount, 0)
                    : number_format($maxAmount, 2) . ' SOL'), 400);
        }

        // Check if payment method belongs to the user
        $paymentMethod = PaymentMethod::find($request->payment_method_id);
        if (! $paymentMethod || $paymentMethod->user_id !== auth()->id()) {
            return $this->error('Invalid payment method', 400);
        }

        // Check if payment method type matches the currency
        if (($request->currency === 'fiat' && $paymentMethod->type !== 'bank') ||
            ($request->currency === 'crypto' && $paymentMethod->type !== 'crypto')) {
            return $this->error('Payment method type does not match the currency', 400);
        }

        // Check running funds limit (max 3, excluding recommitments)
        $maxRunningFunds = 3;
        $runningFundsCount = Fund::getRunningFundsCount($user->id, $request->previous_fund_id);

        if ($runningFundsCount >= $maxRunningFunds) {
            return $this->error('You have reached the maximum limit of ' . $maxRunningFunds . ' running funds. Please wait for existing funds to mature or cancel them before creating new ones.', 400);
        }

        // Get platform settings
        $platformFeePercentage = PlatformSetting::getSetting('platform_fee_percentage');
        $growthRate = $request->currency === 'fiat'
            ? PlatformSetting::getSetting('fiat_growth_rate')
            : PlatformSetting::getSetting('crypto_growth_rate');

        // Get maturity days based on user privileges
        $maturityDays = $privilegeService->getMaturityDays($user);

        // Calculate growth amount
        $growthAmount = $request->amount * ($growthRate / 100);

        // Calculate referral bonus limit
        // Ensure that referral bonus + growth amount doesn't exceed 100% of fund amount
        $maxBonusAndGrowthLimit = $request->amount; // 100% of fund amount
        $referralBonusLimit = max(0, $maxBonusAndGrowthLimit - $growthAmount);

        try {
            DB::beginTransaction();

            // Handle previous fund validation if provided
            $eligiblePrevFund = null;
            if ($request->has('previous_fund_id') && ! empty($request->previous_fund_id)) {
                $eligiblePrevFund = $this->validateAndGetPreviousFund(
                    $request->previous_fund_id,
                    $user->id,
                    $request->amount,
                    $request->currency
                );

                // If validation fails, the method will return an error response
                if ($eligiblePrevFund instanceof JsonResponse) {
                    DB::rollBack();

                    return $eligiblePrevFund;
                }
            }

            // Create the fund
            $fund = Fund::create([
                'user_id' => $user->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'payment_method_id' => $request->payment_method_id,
                'platform_fee_percentage' => $platformFeePercentage,
                'start_date' => null, // Will be set when fund is completed
                'maturity_date' => null, // Will be set when fund is completed
                'maturity_days' => $maturityDays,
                'growth_percentage' => $growthRate,
                'growth_amount' => $growthAmount,
                'referral_bonus_limit' => $referralBonusLimit,
                'is_eligible_for_withdrawal' => false,
                'amount_matched' => 0,
                'status' => 'pending', // Will change to 'matched' when fully matched, then 'completed' after payment
                'prev_fund_id' => $eligiblePrevFund ? $eligiblePrevFund->id : null,
            ]);

            // If we found an eligible previous fund, update its next_fund_id
            if ($eligiblePrevFund) {
                $eligiblePrevFund->next_fund_id = $fund->id;
                $eligiblePrevFund->save();
            }

            // Update user stats
            $userStat = UserStat::firstOrCreate(['user_id' => $user->id], [
                'updated_at' => now(),
            ]);
            if ($request->currency === 'fiat') {
                $userStat->incrementTotalFiatFunded($request->amount);
            } else {
                $userStat->incrementTotalCryptoFunded($request->amount);
            }

            DB::commit();

            return $this->success($fund, 'Fund created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create fund: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified fund.
     */
    public function show(string $id): JsonResponse
    {
        // First get the fund to access its user_id
        $fund = Fund::find($id);

        if (! $fund) {
            return $this->notFound('Fund not found');
        }

        // Now load relationships with proper filtering
        $fund->load([
            'user:id,full_name,email,phone',
            'paymentMethod',
            'paymentMatches.paymentMethod',
            'paymentMatches.disputes',
            'paymentMatches.fundUser:id,full_name,email,phone',
            'paymentMatches.withdrawUser:id,full_name,email,phone',
            'nextFund',
            'userWithdraw',
            'withdraws' => function ($query) use ($fund) {
                $query->whereDoesntHave('platformFee')
                    ->where('user_id', $fund->user_id);
            },
        ]);

        // Check if the fund belongs to the authenticated user
        if ($fund->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to view this fund');
        }

        return $this->success($fund, 'Fund retrieved successfully');
    }

    /**
     * Cancel the specified fund.
     */
    public function cancel(string $id): JsonResponse
    {
        $fund = Fund::find($id);

        if (! $fund) {
            return $this->notFound('Fund not found');
        }

        // Check if the fund belongs to the authenticated user
        if ($fund->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to cancel this fund');
        }

        // Check if the fund is in a cancellable state
        if ($fund->status !== 'pending') {
            return $this->error('Only pending funds can be cancelled', 400);
        }

        try {
            DB::beginTransaction();

            // Cancel the fund
            $fund->status = 'cancelled';
            $fund->save();

            // Update user stats
            $userStat = UserStat::where('user_id', $fund->user_id)->first();
            if ($userStat) {
                if ($fund->currency === 'fiat') {
                    $userStat->decrementTotalFiatFunded($fund->amount);
                } else {
                    $userStat->decrementTotalCryptoFunded($fund->amount);
                }
            }

            // Cancel any referral bonuses associated with this fund
            ReferralBonus::where('fund_id', $fund->id)->delete();

            DB::commit();

            return $this->success($fund, 'Fund cancelled successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to cancel fund: ' . $e->getMessage());
        }
    }

    /**
     * Get funds statistics.
     * Admins see platform-wide statistics, users see their own statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            // Check if user is admin to determine query scope
            if (auth()->user()->isAdmin()) {
                // Admin gets platform-wide statistics
                $query = Fund::query();

                // Allow admin to filter by user_id if provided
                if ($request->has('user_id') && ! empty($request->user_id)) {
                    $query->where('user_id', $request->user_id);
                }

                // Allow admin to filter by user email if provided
                if ($request->has('email') && ! empty($request->email)) {
                    $query->whereHas('user', function ($q) use ($request) {
                        $q->where('email', 'like', '%' . $request->email . '%');
                    });
                }
            } else {
                // Users only see their own statistics
                $query = Fund::where('user_id', auth()->id());
            }

            // Optional date range filtering
            if ($request->has('date_from') && ! empty($request->date_from)) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && ! empty($request->date_to)) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $funds = $query->get();

            // Group funds by status for efficient calculations
            $fundsByStatus = $funds->groupBy('status');

            // Initialize status groups to ensure all statuses are represented
            $statuses = ['pending', 'matched', 'completed', 'cancelled'];
            foreach ($statuses as $status) {
                if (! $fundsByStatus->has($status)) {
                    $fundsByStatus[$status] = collect();
                }
            }

            // Helper function to calculate improved status stats
            $calculateStatusStats = function ($statusFunds) {
                $fiatFunds = $statusFunds->filter(fn ($f) => $f->currency === 'fiat');
                $cryptoFunds = $statusFunds->filter(fn ($f) => $f->currency === 'crypto');

                return [
                    'total_count' => $statusFunds->count(),
                    'count' => [
                        'fiat' => $fiatFunds->count(),
                        'crypto' => $cryptoFunds->count(),
                    ],
                    'amount' => [
                        'fiat' => round($fiatFunds->sum('amount'), 2),
                        'crypto' => round($cryptoFunds->sum('amount'), 6),
                    ],
                ];
            };

            // Calculate overall statistics
            $overallStats = $calculateStatusStats($funds);

            $statistics = [
                'overview' => [
                    'total_count' => $overallStats['total_count'],
                    'count' => $overallStats['count'],
                    'amount' => $overallStats['amount'],
                ],
                'statuses' => [
                    'pending' => $calculateStatusStats($fundsByStatus['pending']),
                    'matched' => $calculateStatusStats($fundsByStatus['matched']),
                    'completed' => $calculateStatusStats($fundsByStatus['completed']),
                    'cancelled' => $calculateStatusStats($fundsByStatus['cancelled']),
                ],
            ];

            return $this->success($statistics, 'Fund statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to retrieve fund statistics: ' . $e->getMessage());
        }
    }

    /**
     * Get payment match timeout information for a specific fund.
     */
    public function getTimeoutInfo(string $id): JsonResponse
    {
        try {
            // First check if fund exists and user has access
            $fund = Fund::find($id);

            if (! $fund) {
                return $this->notFound('Fund not found');
            }

            // Check if user owns this fund or is admin
            if (! auth()->user()->isAdmin() && $fund->user_id !== auth()->id()) {
                return $this->forbidden('Unauthorized access to fund');
            }

            $timeoutService = new PaymentMatchTimeoutService;
            $timeoutInfo = $timeoutService->getFundTimeoutInfo($id);

            return $this->success($timeoutInfo, 'Fund timeout information retrieved successfully');

        } catch (\Exception $e) {
            return $this->serverError('Failed to retrieve timeout information: ' . $e->getMessage());
        }
    }

    /**
     * Get funding schedule information for users.
     */
    public function getFundingScheduleInfo(): JsonResponse
    {
        $fundingScheduleEnabled = PlatformSetting::getSetting('funding_schedule_enabled', false);

        if (! $fundingScheduleEnabled) {
            return $this->success([
                'schedule_enabled' => false,
                'funding_allowed' => true,
                'message' => 'Funding is available 24/7',
            ], 'Funding schedule information retrieved successfully');
        }

        $fundingSchedulePeriods = PlatformSetting::getSetting('funding_schedule_periods', []);

        // If periods are stored as JSON string, decode them
        if (is_string($fundingSchedulePeriods)) {
            $fundingSchedulePeriods = json_decode($fundingSchedulePeriods, true) ?: [];
        }

        $isFundingAllowed = $this->isFundingAllowedBySchedule();

        // Get current time in WAT for display
        $currentTime = now()->setTimezone('Africa/Lagos');
        $currentTimeString = $currentTime->format('H:i');

        // Find next available period if funding is not currently allowed
        $nextAvailablePeriod = null;
        if (! $isFundingAllowed && ! empty($fundingSchedulePeriods)) {
            foreach ($fundingSchedulePeriods as $period) {
                $startTime = $period['start_time'] ?? '00:00';
                if ($currentTimeString < $startTime) {
                    $nextAvailablePeriod = $startTime;
                    break;
                }
            }

            // If no period found for today, use the first period of next day
            if (! $nextAvailablePeriod && ! empty($fundingSchedulePeriods)) {
                $nextAvailablePeriod = $fundingSchedulePeriods[0]['start_time'] ?? '00:00';
            }
        }

        return $this->success([
            'schedule_enabled' => true,
            'funding_allowed' => $isFundingAllowed,
            'current_time' => $currentTimeString,
            'periods' => $fundingSchedulePeriods,
            'next_available_time' => $nextAvailablePeriod,
            'message' => $isFundingAllowed
                ? 'Funding is currently allowed'
                : 'Funding is currently not allowed',
        ], 'Funding schedule information retrieved successfully');
    }

    // ======================================================================
    // Private utility methods
    // ======================================================================

    /**
     * Check if funding is currently allowed based on schedule settings.
     */
    private function isFundingAllowedBySchedule(): bool
    {
        // Check if funding schedule is enabled
        $fundingScheduleEnabled = PlatformSetting::getSetting('funding_schedule_enabled', false);

        if (! $fundingScheduleEnabled) {
            return true; // If schedule is disabled, funding is always allowed
        }

        // Get the configured periods
        $fundingSchedulePeriods = PlatformSetting::getSetting('funding_schedule_periods', []);

        // If periods are stored as JSON string, decode them
        if (is_string($fundingSchedulePeriods)) {
            $fundingSchedulePeriods = json_decode($fundingSchedulePeriods, true) ?: [];
        }

        if (empty($fundingSchedulePeriods)) {
            return false; // If no periods are defined and schedule is enabled, no funding allowed
        }

        // Get current time in WAT (West Africa Time - UTC+1)
        $currentTime = now()->setTimezone('Africa/Lagos');
        $currentTimeString = $currentTime->format('H:i'); // 14:30 format

        foreach ($fundingSchedulePeriods as $period) {
            // Check if current time is within the allowed time range
            $startTime = $period['start_time'] ?? '00:00';
            $endTime = $period['end_time'] ?? '23:59';

            if ($currentTimeString >= $startTime && $currentTimeString <= $endTime) {
                return true; // Found a valid period
            }
        }

        return false; // No valid period found
    }

    /**
     * Validate and get the previous fund for recommitment.
     */
    private function validateAndGetPreviousFund(string $previousFundId, string $userId, float $newFundAmount, string $currency): Fund|JsonResponse
    {
        // Get the previous fund
        $previousFund = Fund::find($previousFundId);

        if (! $previousFund) {
            return $this->error('Previous fund not found', 400);
        }

        // 1. Check if the previous fund belongs to the user
        if ($previousFund->user_id !== $userId) {
            return $this->error('You do not have permission to use this fund for recommitment', 403);
        }

        // 2. Check if the previous fund is completed
        if ($previousFund->status !== 'completed') {
            return $this->error('Previous fund must be completed to create a recommitment fund', 400);
        }

        // 3. Check if previous fund has the same currency as current fund
        if ($previousFund->currency !== $currency) {
            return $this->error('Previous fund currency must match the current fund currency', 400);
        }

        // 4. Check if current fund amount is >= previous fund amount
        if ($newFundAmount < $previousFund->amount) {
            $currencySymbol = $currency === 'fiat' ? 'NGN ' : '';
            $currencyUnit = $currency === 'crypto' ? ' SOL' : '';

            return $this->error(
                'Recommitment fund amount must be greater than or equal to the previous fund amount of ' .
                $currencySymbol . number_format($previousFund->amount, $currency === 'crypto' ? 6 : 0) . $currencyUnit,
                400
            );
        }

        // 5. Check if the previous fund already has a next fund
        if ($previousFund->next_fund_id !== null) {
            return $this->error('Previous fund already has a recommitment fund', 400);
        }

        return $previousFund;
    }
}
